import { MapLocation } from './base-map-location.js';
import { MarkerManager } from './marker-manager.js';
import { LocationUtils } from './location-utils.js';

class MapLocationHome extends MapLocation {
  constructor() {
    super();
    this.markerManager = null;

    // Use shared debounce from utils
    this.debouncedLoad = LocationUtils.debounce(() => {
      if (this.markerManager && this.allLocations) {
        this.markerManager.loadMarkersInBounds(this.allLocations);
      }
    }, 300);
  }

  async connectedCallback() {
    await this.initializeMapLocation();
  }

  async initializeMapLocation() {
    await super.initializeMapLocation();

    if (this.allLocations && this.map) {
      this.markerManager = new MarkerManager(this.map);

      // Initial load of markers within current map bounds
      this.markerManager.loadMarkersInBounds(this.allLocations);

      // Debounced update on map idle (e.g., after drag/zoom)
      this.map.addListener('idle', this.debouncedLoad);
    }
  }
}

if (!customElements.get('map-location-home')) {
  customElements.define('map-location-home', MapLocationHome);
}
export { MapLocationHome };
