// API constants
const API_METHODS = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
};

const HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  CONTENT_APPLICATION_JSON: 'application/json',
  CONTENT_APPLICATION_URLENCODED: 'application/x-www-form-urlencoded',
  REDIRECT_MANUAL: 'manual',
  DATA_TYPE: 'html',
  STYKU_AUTHORIZATION: 'X-Styku-Authorization',
  BEARER: 'Bearer',
};

const BASE_URL = `${location.origin}`;

function createApiPayload(
  endpoint,
  {
    method = API_METHODS.POST,
    contentType = HEADERS.CONTENT_APPLICATION_JSON,
    accessToken = null,
    ...otherOptions
  } = {}
) {
  const payload = {
    endPoint: `${BASE_URL}${endpoint}`,
    method,
    headers: {
      [HEADERS.CONTENT_TYPE]: contentType,
    },
    ...otherOptions,
  };

  if (accessToken) {
    payload.headers[HEADERS.STYKU_AUTHORIZATION] = `${HEADERS.BEARER} ${accessToken}`;
  }

  return payload;
}

function shopifyAPIPayload(endpoint) {
  return createApiPayload(endpoint, {
    method: API_METHODS.POST,
    contentType: HEADERS.CONTENT_APPLICATION_URLENCODED,
    dataType: HEADERS.DATA_TYPE,
    async: true,
  });
}

// API payloads
const API_PAYLOADS = {
  VERIFY_PROFILE: createApiPayload('/apps/styku/account/email/verify-profile'),
  EMAIL_VERIFICATION_CODE: createApiPayload('/apps/styku/account/email/verify-code'),
  RESEND_EMAIL_VERIFICATION_CODE: createApiPayload('/apps/styku/account/email/send-verification-code'),
  SECURE_ACCOUNT: createApiPayload('/apps/styku/account/secure'),
  LOGIN: createApiPayload('/apps/styku/account/login'),
  RESET_PASSWORD: createApiPayload('/apps/styku/account/reset-password'),
  SCHEDULE_SCAN: createApiPayload('/apps/styku/schedule-scan'),
  CHANGE_PASSWORD: (accessToken) =>
    createApiPayload('/apps/styku/profile/change-password', { method: API_METHODS.PUT, accessToken: accessToken }),
  SIGN_OUT: (accessToken) =>
    createApiPayload('/apps/styku/account/sign-out', { method: API_METHODS.POST, accessToken: accessToken }),
  GET_PROFILE: (accessToken) =>
    createApiPayload('/apps/styku/profile', { method: API_METHODS.GET, accessToken: accessToken }),

  // Appstle API payloads
  GET_SUBSCRIPTIONS: createApiPayload('/apps/styku/subscriptions', { method: API_METHODS.GET }),

  // Shopify API payloads
  SHOPIFY_CUSTOMER_LOGIN: shopifyAPIPayload('/account/login'),
  SHOPIFY_CUSTOMER_SIGN_OUT: createApiPayload('/account/logout', { method: API_METHODS.GET }),
  ADD_ITEM_TO_CART: createApiPayload('/cart/add.js'),
  CHANGE_CART_ITEM: createApiPayload('/cart/change.js'),
  UPDATE_CART_ITEM: createApiPayload('/cart/update.js'),
  GET_CART_ITEM: createApiPayload('/cart.js', { method: API_METHODS.GET }),
};

// Error status codes
const ERROR_CODES = Object.freeze({
  INVALID_EMAIL: 601,
  UNABLE_TO_SEND_EMAIL: 603,
  INVALID_VERIFICATION_CODE: 604,
  ALREADY_SECURED: 605,
  WEAK_PASSWORD: 607,
  INVALID_PASSWORD: 613,
  NEW_PASSWORD_SAME_AS_OLD: 614,
  INVALID_USERNAME_OR_PASSWORD: 630,
  USER_NOT_FOUND: 631,
});

// Success status codes
const SUCCESS_CODE = Object.freeze({
  SUCCESS: 200,
});

const SESSION_KEY = {
  PROFILE: 'profile',
};

const HASH_KEY = {
  MEMBERSHIP: '#membership',
};

const LOCAL_STORAGE_KEY = {
  ACCESS_TOKEN: 'access_token',
  LOGGED_IN_USER: 'logged_in_user',
  LOCATION_ID: 'location_id',
};

const QUERY_PARAMS_KEY = {
  CHECKOUT_URL: 'checkout_url',
};

const PRODUCT_TYPE = {
  HEALTH_PASS: 'health-pass',
  PAY_PER_SCAN: 'pay-per-scan',
  HEALTH_PRODUCT: 'health-products',
};

// Page URLs
const PAGE_URLS = {
  EMAIL_VERIFY: '/pages/email-verify',
  PRICING: '/pages/pricing',
  HEALTH_PRODUCT: '/collections/health-products',
  CART: '/cart',
  RESET_PASSWORD: '/pages/reset-password',
  ACCOUNT: '/account',
  SHOPIFY_LOGIN: '/account/login',
  SHOPIFY_REGISTER: '/account/register',
  SUBSCRIPTIONS: '/apps/subscriptions',
  SUBSCRIPTIONS_LIST: '/apps/subscriptions#/subscriptions/list',
};

// Customer Subscription Status
const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  CANCELLED: 'cancelled',
};

const ON_CHANGE_DEBOUNCE_TIMER = 300;
