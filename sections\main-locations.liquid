<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@19.2.16/build/css/intlTelInput.css">
{{ 'location-map.css' | asset_url | stylesheet_tag }}
{{ 'phone-number-validation.css' | asset_url | stylesheet_tag }}
{{ 'modal.css' | asset_url | stylesheet_tag }}

<style>
  #map {
    height: 600px;
    width: 100%;
  }

  .rotate-y-animation {
    animation: rotateAnimation 2s infinite ease-in;
    transform-style: preserve-3d;
    animation-direction: alternate-reverse;
  }

  .gm-style .gm-style-iw-c {
    flex-direction: row-reverse !important;
    padding-left: 18px !important;
  }

  .gm-style-iw-d {
    overflow: hidden !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  @keyframes rotateAnimation {
    0% {
      transform: rotateY(0deg);
    }

    100% {
      transform: rotateY(45deg);
    }
  }
  @media (max-width: 768px) {
    .heading-block .heading-level-1 {
      font-size: 28px;
    }

    .heading-block .paragraph-text-responsive {
      max-width: 90%;
      margin: auto;
    }
  }
</style>

{% liquid
  if customer
    assign scan_counts = customer.metafields.styku.available_scan_bundles.value
    assign has_scan_bundle = false
    if scan_counts > 0
      assign has_scan_bundle = true
    endif
  endif
%}
<section>
  <div class="container px-4 lg:px-0">
    <div class="heading-block text-center mt-[18px] mb-0 md:mt-9 md:mb-9">
      <h2 class="heading-level-1">{{ 'sections.locations.title' | t }}</h2>
      <p class="paragraph-text-responsive mt-2">{{ 'sections.locations.subtitle' | t }}</p>
    </div>
    <map-location-main
      data-default-lat="{{ section.settings.default_lat }}"
      data-default-lng="{{ section.settings.default_lng }}"
      data-default-radius="{{ settings.default_radius }}"
      data-default-zoom="{{ section.settings.default_zoom }}"
      data-default-max-zoom="{{ section.settings.default_max_zoom }}"
      data-default-zoom-mobile="{{ section.settings.default_zoom_mobile }}"
      data-default-max-zoom-mobile="{{ settings.default_max_zoom_mobile }}"
      data-marker-center-map-zoom-level="{{ settings.marker_center_map_zoom_level }}"
      data-marker-center-map-zoom-level-mobile="{{ settings.marker_center_map_zoom_level_mobile }}"
      data-enable-radius-search="true"
      data-enable-search-input="true"
      data-enable-radius-filter="true"
      data-enable-business-type-filter="true"
      data-enable-update-queary-params="true"
      data-enable-near-me="true"
      data-sidebar-location-list="true"
      class="main-wrapper w-full p-[2px]"
    >
      <div class="block my-6 lg:hidden lg:my-0">
        <button
          id="filterIcon"
          data-toggle="modal-selector"
          data-target="filterModalSelector"
          class="filter-icon-block flex gap-2 p-2.5 px-4 w-full justify-center items-center align-middle border border-gray-2 rounded-md"
          role="button"
          data-loading="false"
        >
          <div class="filter-block flex gap-2">
            {{- 'icon-filters.svg' | inline_asset_content -}}
            <span class="text-base">{{ 'general.filters_text' | t }}</span>
          </div>
          <div class="active-filter-block leading-[0px] hidden">
            <span
              class="filter-count inline-flex justify-center items-center w-[20px] h-[20px] text-sm font-normal leading-[0px]  bg-gradient-to-r rounded-full from-[#C8277D] to-[#D22725] text-white"
            ></span>
            <span class="text-base leading-[0px]">{{ 'general.activeFilters' | t }}</span>
          </div>
        </button>
      </div>
      <div class="modal-overlay cursor-pointer lg:mb-6" data-modal="filterModalSelector">
        <div class="modal w-[90%] min-h-[520px] p-[26px] content-between shadow-sm rounded-lg lg:w-full lg:min-h-fit lg:p-0 grid lg:content-normal bg-white lg:shadow-none lg:rounded-none">
          <div class="modal-content w-full">
            <div class="modal-header block lg:hidden">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-8">
                  {{ 'general.filters_text' | t }}
                </h3>
                <button data-dismiss="modal-selector" class="close-modal cursor-pointer hover:scale-125">
                  {{- 'icon-cross.svg' | inline_asset_content -}}
                </button>
              </div>
            </div>
            <div class="modal-content">
              {% render 'locations-filters' %}
            </div>
          </div>
          <div class="flex justify-between gap-2 lg:hidden">
            <button role="button" id="resetAllFilters" class="button-secondary w-[50%]">Reset</button>
            <button role="button" id="applyAllFilters" class="button-primary-gradient w-[50%]">Apply</button>
          </div>
        </div>
      </div>
      <div class="map-box-section grid gap-6 md:flex">
        <div
          id="sidebar"
          class="sidebar-location md:flex-none"
          {% if customer %}
            data-has-scan-bundle="{{ has_scan_bundle }}"
          {% endif %}
        ></div>
        <div id="map" class="map-container md:flex"></div>
      </div>
    </map-location-main>
  </div>
</section>

{% render 'schedule-scan-modal' %}

<script src="{{ 'locations-filter.js' | asset_url }}" defer></script>
<script src="{{ 'modal.js' | asset_url }}" async></script>
<!-- Initialize the intlTelInput plugin -->
<script src="https://cdn.jsdelivr.net/npm/intl-tel-input@19.2.16/build/js/intlTelInput.min.js" defer></script>
<script src="{{ 'schedule-scan.js' | asset_url }}" defer></script>
{% schema %}
{
  "name": "Locations",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "default_lat",
      "label": "Add default Latitude",
      "default": "34.0424537"
    },
    {
      "type": "text",
      "id": "default_lng",
      "label": "Add default Longitude",
      "default": "-118.2510634"
    },
    {
      "type": "range",
      "id": "default_zoom",
      "label": "t:settings_schema.location.settings.default_zoom.label",
      "info": "Controls the initial zoom level when the map loads on desktop.",
      "min": 5,
      "max": 18,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "default_max_zoom",
      "label": "t:settings_schema.location.settings.default_zoom.label",
      "info": "Controls the initial zoom level when the map loads on desktop.",
      "min": 8,
      "max": 22,
      "step": 1,
      "default": 18
    },
    {
      "type": "range",
      "id": "default_zoom_mobile",
      "label": "t:settings_schema.location.settings.default_zoom_mobile.label",
      "info": "Controls the initial zoom level when the map loads on mobile.",
      "min": 4,
      "max": 18,
      "step": 1,
      "default": 9
    }
  ]
}
{% endschema %}
