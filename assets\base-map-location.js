import { GoogleMapsLoader, LocationApiService, MapLoader } from './locations-services.js';

class MapLocation extends HTMLElement {
  // Constructor initializes the map location component.
  constructor() {
    super();
    this.mapLoader = new MapLoader(this.querySelector('.map-box-section'));
    this.initializeProperties();
  }

  // Initializes shared properties and configurations for the map location component.
  initializeProperties() {
    this.isMobile = window.matchMedia('(max-width: 768px)').matches;
    this.geolocationErrorMessage = window.utilsString.locationsString.errorMessage;
    this.locationString = window.utilsString.locationsString;
    this.locationSettings = this.locationString.settings;

    this.setDefaultLocationValues();
    this.setSelectedLocationValues();
    this.setMapZoomLevels();
  }

  // Sets the default values for location properties.
  setDefaultLocationValues() {
    const { defaultRadius = 'Infinity', defaultLat = '34.052235', defaultLng = '-118.243683' } = this.dataset;

    this.isEnableRadiusFilter = this.dataset.enableRadiusFilter === 'true';
    this.defaultRadius = this.isEnableRadiusFilter ? Number(defaultRadius) : Infinity;
    this.defaultBusinessType = null;

    this.defaultLocationCoordinates = {
      lat: Number(defaultLat),
      lng: Number(defaultLng),
    };
  }

  // Sets the initial values for selected location properties.
  setSelectedLocationValues() {
    this.selectedNearByMe = null;
    this.selectedInputSearch = null;
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.selectedRadius = this.defaultRadius;
    this.selectedBusinessType = this.defaultBusinessType;
    this.googleMapId = this.locationSettings.googleMapId;
  }

  // Sets the zoom levels for the map based on the device type.
  setMapZoomLevels() {
    const zoom = this.isMobile ? this.dataset.defaultZoomMobile : this.dataset.defaultZoom;
    const maxZoom = this.isMobile ? this.dataset.defaultMaxZoomMobile : this.dataset.defaultMaxZoom;
    const centerZoom = this.isMobile
      ? this.dataset.markerCenterMapZoomLevel
      : this.dataset.markerCenterMapZoomLevelMobile;

    this.zoomLevel = Number(zoom) || 10;
    this.maxZoomLevel = Number(maxZoom) || 10;
    this.markerCenterMapZoomLevel = Number(centerZoom) || 10;
  }

  /**
   * Lifecycle method that runs when the component is added to the DOM.
   * - Initializes the map location.
   * - Binds UI events for user interactions.
   * - Parses URL query parameters if enabled.
   */
  async connectedCallback() {
    await this.initializeMapLocation();
  }

  /**
   * Initializes the map location by loading the Google Maps API,
   * fetching location data, and rendering the map.
   */
  async initializeMapLocation() {
    const { googleApiKey, locationUrl, filterCountries } = this.locationSettings;

    if (!googleApiKey || !locationUrl) {
      this.mapLoader.hideLoader();
      return;
    }

    try {
      this.mapLoader.showLoader();

      await GoogleMapsLoader.loadGoogleMapsAPI({
        key: googleApiKey,
        libraries: 'maps,geometry,marker',
        markerClusterer: true,
      });

      // Fetch location data from the API
      const locations = await LocationApiService.fetchLocations(locationUrl);

      if (!locations) {
        throw new Error('Unable to fetch locations data');
      }

      const allowedCountries = filterCountries?.split(',').map((country) => country.trim().toLowerCase()) ?? [];

      this.allLocations = Array.isArray(locations)
        ? locations.filter((location) => {
            const isInAllowedCountry =
              allowedCountries.length === 0 || allowedCountries.includes(location.country?.trim()?.toLowerCase());
            return isInAllowedCountry;
          })
        : [];

      // Initialize the map inside the specified container
      await this.initializeMap(this.querySelector('.map-container'));
    } catch (error) {
      console.error('Error initializing map location:', error);
    } finally {
      this.mapLoader.hideLoader();
    }
  }

  /**
   * Initializes the Google Map instance within the given container.
   * Configures various map settings such as zoom, controls, and map ID.
   * Otherwise, it filters and loads locations based on the selected coordinates.
   */
  async initializeMap(container) {
    this.mapOptions = {
      center: this.defaultLocationCoordinates,
      zoom: this.zoomLevel,
      zoomControl: true,
      draggable: true,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      minZoom: this.zoomLevel,
      maxZoom: this.maxZoomLevel,
      mapId: this.googleMapId,
      disableDefaultUI: true,
    };

    this.map = new google.maps.Map(container, this.mapOptions);
  }
}
export { MapLocation };
