class MarkerManager {
  constructor(map) {
    this.map = map;
    this.markerClusterer = null;
    this.lastBounds = null;
  }

  createMarkerElement(location) {
    const container = document.createElement('div');
    const img = document.createElement('img');
    img.setAttribute('loading', 'lazy');
    img.setAttribute('alt', location.companyName);
    img.setAttribute('aria-label', location.companyName);
    img.className = 'marker-icon';
    img.src = location.isHealthPassPartner
      ? 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/marker-primary-gradient.svg'
      : 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/secondary-marker.svg';
    container.appendChild(img);

    return container;
  }

  createMarker(location) {
    const element = this.createMarkerElement(location);
    const marker = new google.maps.marker.AdvancedMarkerElement({
      position: {
        lat: parseFloat(location.latitude),
        lng: parseFloat(location.longitude),
      },
      zIndex: location.isHealthPassPartner ? 1 : 0,
      title: location.companyName,
      content: element,
    });
    marker.location = location;
    return marker;
  }

  initMarkerClusterer(markers) {
    const renderer = {
      render: ({ count, position, markers, map }) => {
        const el = document.createElement('div');
        el.className = 'custom-cluster';
        el.innerHTML = `<div class="cluster-content"><span class="cluster-count">${count}</span></div>`;
        el.addEventListener('click', () => {
          const bounds = new google.maps.LatLngBounds();
          markers.forEach((m) => bounds.extend(m.position));
          map.fitBounds(bounds, { padding: 18 });
        });
        return new google.maps.marker.AdvancedMarkerElement({
          position,
          content: el,
        });
      },
    };

    if (!this.markerClusterer) {
      this.markerClusterer = new markerClusterer.MarkerClusterer({
        markers,
        map: this.map,
        algorithm: new markerClusterer.GridAlgorithm({ gridSize: 38, maxZoom: 13 }),
        renderer,
      });
    } else {
      this.markerClusterer.clearMarkers();
      this.markerClusterer.addMarkers(markers);
    }
  }

  loadMarkersInBounds(allLocations) {
    const bounds = this.map.getBounds();
    if (!bounds) return;

    const boundsChanged = !this.lastBounds || !bounds.equals(this.lastBounds);
    if (!boundsChanged) return;

    this.lastBounds = bounds;

    const visibleLocations = allLocations.filter((loc) =>
      bounds.contains(new google.maps.LatLng(loc.latitude, loc.longitude))
    );

    const markers = visibleLocations.map((loc) => this.createMarker(loc));

    this.initMarkerClusterer(markers);
  }
}

export { MarkerManager };
