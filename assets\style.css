html {
  box-sizing: border-box;
  font-size: 1rem;
  height: 100%;
}

body {
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  grid-template-columns: 100%;
  min-height: 100%;
  margin: 0;
  font-size: 1rem;
  letter-spacing: 0px;
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
  color: var(--color-gray-8);
}

.address-block p {
  font-size: 16px;
  line-height: normal;
}

.header-logo-block,
.footer-logo-block {
  width: 210px;
}

.HSA-FSA-badge-block {
  background: linear-gradient(92deg, rgba(7, 7, 7, 0.35) -4.2%, rgba(161, 161, 161, 0.35) 107.46%);
}

img.blur-loading {
  width: 100%;
  height: auto;
  opacity: 0.4;
  z-index: 2;
  border-radius: 16px;
  background: linear-gradient(90deg, #eee 25%, #ddd 50%, #eee 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  transition: filter 0.5s ease, opacity 0.5s ease;
}

img.blur-loading.loaded {
  filter: blur(0);
  opacity: 1;
  background: none;
  animation: none;
  z-index: auto;
}

summary::-webkit-details-marker {
  display: none;
}

body.modal-open {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.underline-primary-text {
  display: inline-block;
  background-image: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
}

#shopify-section-header {
  z-index: 10 !important;
  z-index: 10 !important;
  position: sticky !important;
  top: 0 !important;
}

#shopify-section-header header.sticky-header {
  background: var(--color-white) !important;
  border-bottom: 1px solid #e1e9eb;
}

.alert-message-text strong {
  color: var(--color-secondary);
}

#scheduleScanForm .button-primary-gradient {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.button-primary-gradient:hover,
.products-list.card-type-standard-with-buy-now-button .buy-now-button-block button:hover {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),
    linear-gradient(270deg, #c8277d 0%, var(--color-primary) 100%);
}

.button-primary-gradient:focus,
.products-list.card-type-standard-with-buy-now-button .buy-now-button-block button:focus {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),
    linear-gradient(270deg, #c8277d 0%, var(--color-primary) 100%);
  box-shadow: 0px 0px 0px 4px rgba(200, 39, 125, 0.2);
}

.selected-option {
  display: inline-block;
  background-image: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent !important;
  font-weight: 500 !important;
}

.focus-option-item {
  display: inline-block;
  background-image: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent !important;
}

.active-link {
  display: inline-block;
  font-weight: 600 !important;
  background-image: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent !important;
}

.submenu-item.submenu-item-active a.active,
.submenu-item.submenu-item-active a.active .caret-icon svg {
  color: var(--color-primary);
}

.submenu-item.submenu-item-active a.active .caret-icon svg {
  transform: rotate(180deg);
}

#shopify-section-footer {
  margin-top: auto;
}

.button-primary-gradient-outline {
  background: linear-gradient(var(--color-white), var(--color-white)) padding-box, var(--gradient-primary) border-box !important;
  border: 1px solid transparent;
}

.button-primary-gradient-outline:hover:not(.outline-pills) {
  background: linear-gradient(var(--color-primary-light), var(--color-primary-light)) padding-box,
    var(--gradient-primary) border-box !important;
}

.button-primary-gradient-outline:focus:not(.outline-pills) {
  background: #fff;
  box-shadow: 0px 0px 0px 4px rgba(200, 39, 125, 0.2);
}

.button-primary-light:hover:not(.pills) {
  background: #f2b6c0;
}

.button-primary-light:focus:not(.pills) {
  background: var(--Brand-Red-red-50, #fbe8eb);
  box-shadow: 0px 0px 0px 4px rgba(240, 189, 203, 0.2);
}

button.button-primary-light:disabled {
  opacity: 0.5;
}

.chips-primary-gradient-outline {
  background: linear-gradient(var(--color-primary-light), var(--color-primary-light)) padding-box,
    var(--gradient-primary) border-box;
  border: 1px solid transparent;
}

.selected {
  background: linear-gradient(var(--color-primary-light), var(--color-primary-light)) padding-box,
    var(--gradient-primary) border-box !important;
  border: 1px solid transparent !important;
}

.selected-secondary {
  background: var(--color-gray-1) !important;
  border: 1px solid var(--color-gray-2);
}

.outline-secondary {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: var(--color-white);
  border-radius: 96px;
  border: 1px solid #292929;
}

button[disabled] {
  cursor: not-allowed;
}

button[disabled]:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

button:disabled:not(.increment-button):not(.decrement-button):not(.quantity-decrement-button):not(
    .quantity-increment-button
  ) {
  background: linear-gradient(270deg, #f1b6d6 0%, #f3baba 100%);
}

.near-me-icon,
.search-icon {
  opacity: 0.5;
}

/* Styling for the Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px var(--color-white);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  background-color: var(--color-white);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 6px;
  -webkit-box-shadow: inset 0 0 6px #c1c1c1;
  height: 100px;
  background-color: var(--color-white);
}

/* Second variation of scrollbar */
/* Scrollbar css */
.scrollbar {
  margin-bottom: 25px;
}
.scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px #d9d9d9;
  border-radius: 10px;
  background-color: #f0f0f0;
}
.scrollbar::-webkit-scrollbar {
  width: 4px;
  background-color: #f0f0f0;
}
.scrollbar::-webkit-scrollbar-thumb {
  border-radius: 6px;
  -webkit-box-shadow: inset 0 0 6px #d9d9d9;
  background-color: #d9d9d9;
  height: 260px;
}

.link-item:hover {
  background-clip: text;
  color: transparent;
  background-image: var(--gradient-primary);
}

.gm-ui-hover-effect {
  opacity: 0.6;
  outline: none !important;
  border: none !important;
}

.cross-icon svg {
  width: 18px;
  height: 18px;
}

.valid {
  color: #398d1c !important;
}

.invalid {
  color: #d22721 !important;
}

.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.25s ease;
}

.preloader-invisible {
  opacity: 0;
}

.preloader-hidden {
  visibility: hidden;
}

.preloader-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  animation: rotate 1s linear infinite;
}

.preloader-circle::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  inset: 0px;
  border-radius: 50%;
  border: 5px solid var(--color-white);
  animation: spinner 2s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinner {
  0% {
    clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
  }
  25% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
  }
  50% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
  }
  75% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
  }
  100% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
  }
}

[modal-backdrop] {
  background: rgba(5, 5, 5, 0.7) !important;
}

.owl-item.active.center .bg-muted-seafoam {
  background: #4c6a68 !important;
  border-radius: 16px;
}

.owl-item.active.center div {
  color: var(--color-white) !important;
}

.owl-item .bg-muted-seafoam {
  transition: background 0.5s ease, border-radius 0.5s ease;
}

/* Accordian */
.accordion button {
  color: var(--color-primary);
}

.accordion button .icon {
  display: inline-block;
  position: relative;
  right: 0;
  top: 4px;
  width: 24px;
  height: 24px;
  background: linear-gradient(var(--color-white), var(--color-white)) padding-box, var(--gradient-primary) border-box;
  border: 2px solid transparent;
  border-radius: 24px;
}

.accordion button .icon::before {
  display: block;
  position: absolute;
  content: '';
  top: 9px;
  left: 4px;
  width: 12px;
  height: 2px;
  background: currentColor;
}

.accordion button .icon::after {
  display: block;
  position: absolute;
  content: '';
  top: 5px;
  left: 9px;
  width: 2px;
  height: 10px;
  background: currentColor;
}

.accordion button[aria-expanded='true'] .icon::after {
  width: 0;
}

.accordion button[aria-expanded='true'] + .accordion-content {
  opacity: 1;
  max-height: fit-content;
  transition: all 200ms linear;
  will-change: opacity, max-height;
}

.accordion .accordion-content {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: opacity 200ms linear, max-height 200ms linear;
  will-change: opacity, max-height;
}

.accordion-item:last-child,
.accordion-item:last-child .accordion-content {
  margin-bottom: 0px;
}

/* Video Modal */
#modal-video-container {
  position: fixed;
  height: 100dvh;
  width: 100%;
  z-index: 9991;
  opacity: 0;
  visibility: hidden;
  background: rgba(5, 5, 5, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.25s ease;
}

#modal-video-container iframe,
#modal-video-container video {
  border-radius: 12px;
  position: relative;
  box-sizing: border-box;
  background-color: var(--color-black);
  z-index: 10;
  box-shadow: 0 0 19px rgba(0, 0, 0, 0.16);
  display: block;
  max-width: fit-content;
  height: 90vh;
  width: 100%;
  aspect-ratio: 16/9;
}

#closeModalVideo {
  position: absolute;
  right: 28px;
  top: 28px;
  padding: 6px;
  border: 2px solid var(--color-white);
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

#modal-video-container.active {
  opacity: 1;
  visibility: visible;
}

.modal-video-active {
  overflow: hidden;
}

.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* Small devices (phones, 600px and down) */
@media only screen and (max-width: 768px) {
  .header-logo-block,
  .footer-logo-block {
    width: 152px;
  }

  .menu-drawer-navigation-container {
    overflow-y: scroll;
    padding-right: 12px;
  }

  #modal-video-container iframe,
  #modal-video-container video {
    width: 95%;
    height: auto;
  }

  #closeModalVideo {
    position: absolute;
    right: 28px;
    top: 28px;
    padding: 6px;
    border: 2px solid var(--color-white);
    border-radius: 50px;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
  }
}

/* Tooltip */
.tooltip-wrapper .tooltip {
  bottom: 100%;
  background: transparent;
  z-index: 2;
  padding: 16px;
  position: absolute;
}

.tooltip .inner-block {
  max-width: 360px;
  min-width: 360px;
  border-radius: 6px;
  color: var(--color-gray-8);
  background: var(--color-white);
  font-size: 14px;
  margin-bottom: -16px;
  padding: 16px;
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.2);
}

.pricing-table-wrapper .tooltip-wrapper img {
  filter: brightness(0);
}

.tooltip-arrow,
.tooltip-arrow:before {
  background: inherit;
  width: 10px;
  height: 10px;
  position: absolute;
}

.tooltip-arrow {
  visibility: hidden;
}

.tooltip-arrow:before {
  content: '';
  visibility: visible;
  transform: rotate(45deg);
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow {
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow {
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow {
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before {
  visibility: hidden;
}

@media only screen and (max-width: 768px) {
  .tooltip-wrapper .tooltip {
    max-width: 340px;
    min-width: 320px;
    font-size: 12px;
    line-height: 1.3;
  }

  .tooltip .inner-block {
    max-width: 320px;
    min-width: 310px;
  }
}

@media screen and (min-width: 769px) {
  .submenu-item:hover a .caret-icon svg {
    transform: rotate(180deg);
  }

  .submenu-item:hover .submenu {
    opacity: 1;
    visibility: visible;
  }

  .submenu-item:hover a[role='button'] {
    color: var(--color-primary);
  }
}

/* Appstle Customer Portal */
.as-container {
  width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

@media (min-width: 640px) {
  .as-container {
    max-width: 100% !important;
  }
}

@media (min-width: 768px) {
  .as-container {
    max-width: 100% !important;
  }
}

@media (min-width: 1024px) {
  .as-container {
    max-width: 1024px !important;
  }
}

@media (min-width: 1280px) {
  .as-container {
    max-width: 1216px !important;
  }
}

@media (min-width: 1024px) {
  .lg\:as-container {
    width: 100% !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  @media (min-width: 640px) {
    .lg\:as-container {
      max-width: 100% !important;
    }
  }

  @media (min-width: 768px) {
    .lg\:as-container {
      max-width: 100% !important;
    }
  }

  @media (min-width: 1024px) {
    .lg\:as-container {
      max-width: 1024px !important;
    }
  }

  @media (min-width: 1280px) {
    .lg\:as-container {
      max-width: 1216px !important;
    }
  }
}

.app-main.appstle-customer-portal {
  padding: 48px 0 !important;
}

.appstle_order-detail_update-button,
.as-customer-info-name,
.as-order-scheduled-date {
  font-weight: 700 !important;
}

.as-customer-info img {
  width: 48px !important;
}

.as-subscription-detail-wrapper {
  border: 1px solid var(--color-gray-2) !important;
  border-radius: 8px !important;
}

.as-shadow.as-tab-list {
  box-shadow: none !important;
  border-bottom: 1px solid var(--color-gray-2) !important;
  border-radius: 0 !important;
}

.as-selected-tab-primary {
  border-bottom-width: 3px !important;
}

.as-card.as-product-details,
.as-card.as-edit-billing,
.as-card.as-edit-frequency,
.as-card.as-edit-shipping,
.as-card.as-edit-payment,
.appstle-fulfilment-tab-content .as-bg-white.as-shadow,
.appstle-history-tab-content .as-bg-white {
  box-shadow: none !important;
  border: 1px solid var(--color-gray-2) !important;
}

.appstle-subscription-detail-tab-content,
.appstle-fulfilment-tab-content,
.appstle-history-tab-content {
  padding: 1rem !important;
}

.appstle-subscription-detail-tab {
  border-radius: 12px 0 0 0 !important;
}

.appstle-history-tab {
  border-radius: 0 12px 0 0 !important;
}

.as-product-title {
  font-size: 1rem !important;
  font-weight: 700 !important;
}

.appstle-fulfilment-tab-content .as-border-t.as-border-b {
  border: none !important;
}

.as-card_title,
.as-product-details_title,
.as-modal-title.as-modal-header,
.as-modal-body h2,
.appstle-history-tab-content .as-text-lg.as-leading-6.as-font-medium,
.appstle-history-tab-content .as-text-sm.as-text-gray-800.as-mb-2 {
  font-size: 18px !important;
  color: var(--color-secondary) !important;
  font-weight: 700 !important;
  margin-bottom: 12px !important;
}

.as-card_cta {
  background-image: var(--gradient-primary) !important;
  background-clip: text !important;
  font-weight: 700 !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
}

.as-button.as-button--cancelsub.as-button--secondary:focus,
.as-edit-billing_secondary-button:focus,
.as-button_modal-primary.as-cancel-btn-modal {
  box-shadow: 0px 0px 0px 4px rgba(200, 39, 125, 0.2) !important;
}

.as-button_modal-primary.as-cancel-btn-modal {
  color: var(--color-secondary) !important;
}

/* Small devices (phones, 600px and down) */
@media only screen and (max-width: 768px) {
  .appstle-wrapper {
    width: 90%;
    margin: auto;
  }

  .app-main.appstle-customer-portal {
    padding: 32px 0 0 0 !important;
  }

  .as-subscription-detail-wrapper .as-inline-flex.as-h-10.as-items-center {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    padding: 0 !important;
  }
}

.as-unSkip-order.as-button--tertiary,
.as-skip-order.as-button--tertiary {
  background-image: var(--gradient-primary);
  background-clip: text !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
  font-weight: 700 !important;
  font-weight: 700 !important;
}
