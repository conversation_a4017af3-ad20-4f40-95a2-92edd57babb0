export class LocationUtils {
  /**
   * Debounces a function by a specified delay.
   * @param {Function} func - Function to debounce.
   * @param {number} delay - Delay in milliseconds.
   * @returns {Function}
   */
  static debounce(func, delay) {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * Throttles a function to limit its execution rate.
   * @param {Function} func - Function to throttle.
   * @param {number} limit - Time in milliseconds to wait between calls.
   * @returns {Function}
   */
  static throttle(func, limit) {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * Gets the center point of a Google Maps LatLngBounds.
   * @param {google.maps.LatLngBounds} bounds
   * @returns {{ lat: number, lng: number }}
   */
  static getBoundsCenter(bounds) {
    const center = bounds.getCenter();
    return {
      lat: center.lat(),
      lng: center.lng(),
    };
  }

  /**
   * Checks if a LatLng is within a given bounds.
   * @param {{ lat: number, lng: number }} point
   * @param {google.maps.LatLngBounds} bounds
   * @returns {boolean}
   */
  static isPointInBounds(point, bounds) {
    const latLng = new google.maps.LatLng(point.lat, point.lng);
    return bounds.contains(latLng);
  }
}
