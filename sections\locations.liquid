{{ 'location-map.css' | asset_url | stylesheet_tag }}
{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="section-{{ section.id }}-margin">
  <div class="container px-4 lg:px-0">
    <div class="heading-block max-w-[765px] mb-6 md:mb-8 text-center mx-auto flex flex-col items-center">
      <h1 class="section-heading-40 text-secondary w-full md:w-[65%]">
        {{ section.settings.section_title }}
      </h1>
      <div class="mt-2 md:mt-3 text-base w-full md:w-[80%]">
        {{ section.settings.section_description }}
      </div>
    </div>
  </div>
  <map-location-home
    data-default-lat="{{ settings.default_latitude }}"
    data-default-lng="{{ settings.default_longitude }}"
    data-default-radius="{{ settings.default_radius }}"
    data-default-zoom="{{ settings.default_zoom }}"
    data-default-max-zoom="{{ settings.default_max_zoom }}"
    data-default-zoom-mobile="{{ settings.default_zoom_mobile }}"
    data-default-max-zoom-mobile="{{ settings.default_max_zoom_mobile }}"
    data-marker-center-map-zoom-level="{{ settings.marker_center_map_zoom_level }}"
    data-marker-center-map-zoom-level-mobile="{{ settings.marker_center_map_zoom_level_mobile }}"
    data-enable-radius-search="true"
    data-enable-search-input="true"
    data-enable-near-me="true"
    class="relative"
  >
    <div class="filter-wrapper container px-4 lg:px-0 mb-12 md:mb-16">
      <div class=" flex flex-col gap-2 md:flex-row justify-center">
        <div class="search-by-near-me grow-0">
          <button
            type="button"
            class="near-me-button flex justify-center items-center lg:inline-flex bg-white border w-full border-gray-2 text-gray-5 text-base rounded-md py-[10px] px-4 gap-2"
          >
            {{- 'icon-near-me.svg' | inline_asset_content -}}
            {{ 'general.near_me' | t }}
          </button>
        </div>
        <div class="search-by-city w-full md:w-[350px]">
          {% render 'search-input-with-cross-icon' %}
        </div>
      </div>
    </div>
    <div class="main-wrapper w-full relative">
      <div class="map-box-section">
        <div class="map-container w-full h-[392px] md:h-[749px]"></div>
      </div>
    </div>
  </map-location-home>
</div>

{% schema %}
{
  "name": "Locations",
  "class": "index-section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "html",
      "id": "section_title",
      "label": "Title",
      "default": "Access to All Healthpass locations"
    },
    {
      "type": "richtext",
      "id": "section_description",
      "label": "Description (optional)",
      "default": "<p>A network of Healthpass partners and resources to increase accessibility and empower you to take control of your health on your terms.</p>"
    },
    {
      "type": "header",
      "content": "Gogole map settings"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "zoom_map",
      "info": "Enable 'Map zoom' functionality",
      "label": "Zoom"
    },
    {
      "type": "header",
      "content": "Filter settings"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "near_me",
      "info": "Enable 'Near me' functionality",
      "label": "Near me"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "search",
      "info": "Enable search functionality",
      "label": "Search"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "business_type",
      "info": "Enable business type filter functionality",
      "label": "Business Type"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "radius",
      "info": "Enable radius filter functionality",
      "label": "Radius"
    },
    {
      "type": "header",
      "content": "Sidebar settings"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "sidebar",
      "info": "Enable left sidebar functionality",
      "label": "Sidebar"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    }
  ],
  "presets": [
    {
      "name": "Locations"
    }
  ]
}
{% endschema %}
