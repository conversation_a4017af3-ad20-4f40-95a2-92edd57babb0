import { MapLocation } from './base-map-location.js';
import { MarkerManager } from './marker-manager.js';
import { LocationUtils } from './location-utils.js';

class MapLocationMain extends MapLocation {
  constructor() {
    super();
    this.markerManager = null;

    this.debouncedLoad = LocationUtils.debounce(() => {
      if (this.markerManager && this.allLocations) {
        this.markerManager.loadMarkersInBounds(this.allLocations);
      }
    }, 300);
  }

  async connectedCallback() {
    await this.initializeMapLocation();
  }

  async initializeMapLocation() {
    await super.initializeMapLocation();

    if (this.allLocations && this.map) {
      this.markerManager = new MarkerManager(this.map);

      // Initial load
      this.markerManager.loadMarkersInBounds(this.allLocations);

      // Debounced reload on map idle
      this.map.addListener('idle', this.debouncedLoad);
    }
  }
}

if (!customElements.get('map-location-main')) {
  customElements.define('map-location-main', MapLocationMain);
}
