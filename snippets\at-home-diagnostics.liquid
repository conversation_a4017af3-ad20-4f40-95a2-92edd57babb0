{% liquid
  assign completed_status = 'completed'
  assign cancelled_status = 'cancelled'
  assign done_string = 'Done' | upcase
  assign pending_string = 'Pending' | upcase
  assign todo_string = 'Todo' | upcase

  assign has_vital_orders = false
  for order in customer.orders
    if order.metafields.vital.vital_orders
      assign has_vital_orders = true
    endif
  endfor
%}

<div class="at-home-diagnostics space-y-6">
  {% render 'at-home-diagnostics-banner', has_vital_orders: has_vital_orders %}
  {% if has_vital_orders %}
    <div class="rounded-2xl md:border md:border-gray-2 md:p-6">
      <div class="grid grid-cols-1 gap-4 ">
        <div
          id="accordion-open"
          class="flex flex-col gap-6"
          data-accordion="open"
          data-active-classes="text-secondary bg-white !border-[#C8277D] border-b-0"
          data-inactive-classes="text-secondary bg-white rounded-b-xl"
        >
          {% for item in customer.orders %}
            {% for vital_order in item.metafields.vital.vital_orders.value %}
              {% liquid
                assign all_existing_status = vital_order.status_progress.value
                assign tracking_link = vital_order.outbound_tracking_url | default: '/'
                assign disable_status = false
              %}

              {% if vital_order.status == completed_status or vital_order.status == cancelled_status %}
                {% render 'at-home-diagnostics-report', vital_order: vital_order %}
              {% else %}
                <div class="accordion-group">
                  <button
                    type="button"
                    class="flex flex-col md:flex-row md:items-center justify-between w-full p-4 md:p-6 font-bold text-secondary border border-gray-2 rounded-t-xl  gap-3 rounded-b-xl"
                    data-accordion-target="#accordion-open-body-{{- vital_order.order_id -}}"
                    aria-expanded="false"
                    aria-controls="accordion-open-body-{{- vital_order.order_id -}}"
                  >
                    <div class="text-left">
                      <span class="flex items-center text-base">
                        {{- vital_order.product_name -}}
                      </span>
                      <span class="flex items-center text-base">{{- vital_order.sample_id -}}</span>
                      <p class="text-sm text-secondary font-normal">
                        {{- vital_order.updated_at | date: '%b %d %Y %H:%M' }}
                      </p>
                    </div>
                    {% for status in all_existing_status reversed %}
                      {% if status.progress == 'PENDING' %}
                        {% assign current_label = status.status | downcase %}
                        {% break %}
                      {% endif %}
                      {% if status.progress == 'DONE' and status.is_error_status %}
                        {% assign current_label = status.status | downcase %}
                        {% break %}
                      {% endif %}
                    {% endfor %}

                    <div class="flex gap-2 justify-between w-full md:w-auto">
                      <div class="button-primary-gradient-outline text-center outline-pills rounded-full py-1 px-2 leading-[0]">
                        <span class="text-primary-gradient text-sm font-normal">
                          {{- 'vital.events' | append: '.' | append: current_label | append: '.status' | t -}}
                        </span>
                      </div>
                      {% render 'carret-icon', is_data_accordion_icon: true %}
                    </div>
                  </button>
                  <div
                    id="accordion-open-body-{{- vital_order.order_id -}}"
                    class="hidden"
                    aria-labelledby="accordion-open-{{- vital_order.order_id -}}"
                  >
                    <div class="border border-[#C8277D] border-t-0 rounded-b-xl">
                      <div class="p-4 md:p-6 flex flex-col gap-6">
                        {% for status in all_existing_status %}
                          {% assign label = status.status | downcase %}
                          <div class="flex gap-2">
                            <div class="icon-block flex-none w-7">
                              {% if status.progress == done_string and status.is_error_status == true %}
                                <div class="circle error">
                                  <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <circle cx="12" cy="12" r="12" fill="#D22721"/>
                                    <path d="M17.5906 6.39941L11.9906 11.9994M11.9906 11.9994L6.39062 17.5994M11.9906 11.9994L17.5906 17.5994M11.9906 11.9994L6.39062 6.39941" stroke="white" stroke-width="1.18794"/>
                                  </svg>
                                </div>
                              {% elsif status.progress == done_string %}
                                {% render 'green-check-mark-icon', width: '24', height: '24' %}
                              {% elsif status.progress == pending_string %}
                                <img
                                  src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_482382.svg?v=1727170258"
                                  width="24"
                                  height="24"
                                >
                              {% elsif status.progress == todo_string %}
                                {% assign disable_status = true %}
                                <div class="border-gray-2 border-2 rounded-full size-6"></div>
                              {% endif %}
                            </div>
                            <div
                              class="text-block"
                              {% if disable_status == true %}
                                style="opacity: 0.3; user-select: none;"
                              {% endif %}
                            >
                              <h3 class="text-base text-secondary font-bold">
                                {{- 'vital.events' | append: '.' | append: label | append: '.status' | t -}}
                              </h3>
                              {% if disable_status == false %}
                                <p class="paragraph-text">
                                  {{-
                                    'vital.events'
                                    | append: '.'
                                    | append: label
                                    | append: '.message_html'
                                    | t:
                                      test_kit_name: vital_order.product_name,
                                      customer_name: customer.name,
                                      tracking_link: tracking_link
                                  -}}
                                </p>
                              {% endif %}
                            </div>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}
</div>
