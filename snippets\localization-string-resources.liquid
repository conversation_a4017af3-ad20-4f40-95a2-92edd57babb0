<script>
  window.errorMessage = {
    invalidEmail: "{{ 'general.error_message.invalid_email' | t }}",
    unableToSendEmail: "{{ 'general.error_message.unable_to_send_email' | t }}",
    somethingWentWrong: "{{ 'general.error_message.something_went_wrong' | t }}",
    invalidVerificationCode: "{{ 'general.error_message.invalid_verification_code' | t }}",
    invalidPassword: "{{ 'general.error_message.invalid_password' | t }}",
    weakPasswordMissingLetter: "{{ 'general.error_message.weak_password_missing_letter' | t }}",
    oldPasswordNotMatchingCurrent: "{{ 'general.error_message.old_password_not_matching_current' | t }}",
    newPasswordNotSameAsOld: "{{ 'general.error_message.new_password_not_same_as_old' | t }}",
    userNotFound: "{{ 'general.error_message.user_not_found' | t  }}",
    failedToScheduleScan: "{{ 'general.schedule_scan.form.error.submitForm' | t }}",
  };

  window.successMessage = {
    verificationCodeSent: "{{ 'general.success_message.verification_code_sent' | t }}",
    passwordChangedSuccessfully: "{{ 'general.success_message.password_changed_successfully' | t }}",
  };

  window.utilsString = {
    general: {
      seeLess: "{{ 'general.see_less' | t }}",
      seeMore: "{{ 'general.see_more' | t }}",
    },
    forgetPassword: {
      title: "{{ 'sections.forget_password.title' | t }}",
      description: "{{ 'sections.forget_password.description' | t }}",
    },
    locationsString: {
      settings: {
        locationUrl: '{{ settings.location_url }}',
        googleApiKey: '{{ settings.google_api_key }}',
        googleMapId: '{{ settings.google_map_id }}',
        filterCountries: '{{ settings.filter_countries }}',
        defaultLat: '{{ settings.default_latitude }}',
        defaultLng: '{{ settings.default_longitude }}',
        defaultRadius: '{{ settings.default_radius }}',
        defaultZoom: '{{ settings.default_zoom }}',
        defaultMaxZoom: '{{ settings.default_max_zoom }}',
        defaultZoomMobile: '{{ settings.default_zoom_mobile }}',
        defaultMaxZoomMobile: '{{ settings.default_max_zoom_mobile }}',
        markerCenterMapZoomLevel: '{{ settings.marker_center_map_zoom_level }}',
        markerCenterMapZoomLevelMobile: '{{ settings.marker_center_map_zoom_level_mobile }}',
        enableRadiusSearch: '{{ settings.enable_radius_search }}',
        enableSearchInput: '{{ settings.enable_search_input }}',
        enableNearMe: '{{ settings.enable_near_me }}',
        enableRadiusFilter: '{{ settings.enable_radius_filter }}',
        enableBusinessTypeFilter: '{{ settings.enable_business_type_filter }}',
      },
      call: "{{ 'general.call' | t }}",
      healthPassPartner: "{{ 'general.health_pass_partner' | t }}",
      directions: "{{ 'general.directions' | t }}",
      businessOfferings: "{{ 'sections.locations.business_offerings' | t }}",
      businessHours: "{{ 'sections.locations.business_hours' | t }}",
      scanningHours: "{{ 'sections.locations.scanning_hours' | t }}",
      allFacilityType: "{{ 'general.filters_dropdown_text.facility_type' | t }}",
      searchRadius: "{{ 'general.filters_dropdown_text.search_radius_all' | t }}",
      memberScanPrice: "{{ 'sections.locations.member_scan_price' | t }}",
      nonMemberScanPrice: "{{ 'sections.locations.non_member_scan_price' | t }}",
      walkInsWelcome: "{{ 'sections.locations.walk_ins_welcome' | t }}",
      appointmentToScanRequired: "{{ 'sections.locations.appointment_to_scan_required' | t }}",
      closed: "{{ 'sections.locations.closed' | t }}",
      open24: "{{ 'sections.locations.open_24' | t }}",
      notAvailable: "{{ 'sections.locations.not_available' | t }}",
      notFound: {
        businessHours: "{{ 'sections.locations.not_found.business_hours' | t }}",
        scanningHours: "{{ 'sections.locations.not_found.scanner_hours' | t }}",
      },
      noSearchResult: {
        title: "{{ 'general.no_search_result.title' | t }}",
        message: "{{ 'general.no_search_result.message' | t }}",
      },
      errorMessage: {
        permissionDenied: "{{ 'sections.locations.error_message.permission_denied' | t  }}",
        positionUnavailable: "{{ 'sections.locations.error_message.position_unavailable' | t }}",
        timeoutError: "{{ 'sections.locations.error_message.timeout' | t }}",
        unknownError: "{{ 'sections.locations.error_message.unknown_error' | t }}",
      },
      buttons: {
        bookScan: "{{ 'sections.locations.button.book_scan' | t }}",
        inquireNow: "{{ 'sections.locations.button.inquire_now' | t }}",
        purchaseScan: "{{ 'sections.locations.button.purchase_scan' | t }}",
      },
    },
  };
</script>
